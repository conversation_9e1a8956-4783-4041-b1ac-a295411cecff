import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import java.io.{File, PrintWriter}

object CoffeeChainAnalysis {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("Coffee Chain Analysis")
      .master("local[*]")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .getOrCreate()

    import spark.implicits._

    try {
      // 设置日志级别
      spark.sparkContext.setLogLevel("WARN")
      
      println("开始咖啡连锁店数据分析...")
      
      // 输入文件路径（当前目录下的CSV文件）
      val inputPath = "CoffeeChain.csv"
      val outputDir = "/home/<USER>/spark_work/"
      
      // 检查输入文件是否存在
      val inputFile = new File(inputPath)
      if (!inputFile.exists()) {
        println(s"错误: 输入文件不存在 - $inputPath")
        return
      }
      
      // 创建输出目录
      val outputDirFile = new File(outputDir)
      if (!outputDirFile.exists()) {
        outputDirFile.mkdirs()
        println(s"创建输出目录: $outputDir")
      }
      
      println(s"读取咖啡连锁店数据: $inputPath")
      
      // 读取CSV数据
      val df = spark.read
        .option("header", "true")
        .option("inferSchema", "true")
        .csv(inputPath)
      
      // 缓存数据
      df.cache()
      
      println("数据预处理...")
      println(s"总记录数: ${df.count()}")
      println("数据结构:")
      df.printSchema()
      
      println("\n前10行数据:")
      df.show(10)
      
      // 数据清洗 - 移除空值
      val cleanedDF = df.na.drop()
      println(s"清洗后记录数: ${cleanedDF.count()}")
      
      val results = StringBuilder.newBuilder
      results.append("=== 咖啡连锁店数据分析报告 ===\n")
      results.append(s"分析时间: ${java.time.LocalDateTime.now()}\n")
      results.append(s"数据源: $inputPath\n")
      results.append(s"总记录数: ${cleanedDF.count()}\n\n")
      
      // 1. 咖啡销售量排名
      println("\n1. 分析咖啡销售量排名...")
      val salesRanking = cleanedDF.groupBy("Product")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          count("*").alias("Transaction_Count"),
          avg("Coffee Sales").alias("Avg_Sales")
        )
        .orderBy(desc("Total_Sales"))
      
      println("咖啡销售量排名 (前20名):")
      salesRanking.show(20)
      
      results.append("1. 咖啡销售量排名 (前10名):\n")
      salesRanking.take(10).foreach { row =>
        val product = row.getAs[String]("Product")
        val totalSales = row.get(1).asInstanceOf[Number].doubleValue()
        val count = row.getAs[Long]("Transaction_Count")
        val avgSales = row.get(3).asInstanceOf[Number].doubleValue()
        results.append(f"   $product: 总销售额=$totalSales%.2f, 交易次数=$count, 平均销售额=$avgSales%.2f\n")
      }
      results.append("\n")
      
      // 保存销售排名到文件
      salesRanking.coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"${outputDir}coffee_sales_ranking")
      
      // 2. 咖啡销售量和State的关系
      println("\n2. 分析咖啡销售量和State的关系...")
      val stateAnalysis = cleanedDF.groupBy("State")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          count("*").alias("Transaction_Count"),
          avg("Coffee Sales").alias("Avg_Sales"),
          countDistinct("Product").alias("Product_Variety")
        )
        .orderBy(desc("Total_Sales"))
      
      println("各州销售情况:")
      stateAnalysis.show()
      
      results.append("2. 各州销售情况分析:\n")
      stateAnalysis.collect().foreach { row =>
        val state = row.getAs[String]("State")
        val totalSales = row.get(1).asInstanceOf[Number].doubleValue()
        val count = row.getAs[Long]("Transaction_Count")
        val avgSales = row.get(3).asInstanceOf[Number].doubleValue()
        val variety = row.getAs[Long]("Product_Variety")
        results.append(f"   $state: 总销售额=$totalSales%.2f, 交易次数=$count, 平均销售额=$avgSales%.2f, 产品种类=$variety\n")
      }
      results.append("\n")
      
      // 3. 咖啡销售量和Market的关系
      println("\n3. 分析咖啡销售量和Market的关系...")
      val marketAnalysis = cleanedDF.groupBy("Market")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          count("*").alias("Transaction_Count"),
          avg("Coffee Sales").alias("Avg_Sales")
        )
        .orderBy(desc("Total_Sales"))
      
      println("各市场销售情况:")
      marketAnalysis.show()
      
      results.append("3. 各市场销售情况分析:\n")
      marketAnalysis.collect().foreach { row =>
        val market = row.getAs[String]("Market")
        val totalSales = row.get(1).asInstanceOf[Number].doubleValue()
        val count = row.getAs[Long]("Transaction_Count")
        val avgSales = row.get(3).asInstanceOf[Number].doubleValue()
        results.append(f"   $market: 总销售额=$totalSales%.2f, 交易次数=$count, 平均销售额=$avgSales%.2f\n")
      }
      results.append("\n")
      
      // 4. 咖啡的平均利润和售价分析
      println("\n4. 分析咖啡的平均利润和售价...")
      val profitAnalysis = cleanedDF.select(
        avg("Coffee Sales").alias("Avg_Sales"),
        avg("Profit").alias("Avg_Profit"),
        avg("Cogs").alias("Avg_COGS"),
        (avg("Profit") / avg("Coffee Sales") * 100).alias("Profit_Margin_Percent")
      )
      
      println("整体利润和售价分析:")
      profitAnalysis.show()
      
      val profitRow = profitAnalysis.collect()(0)
      val avgSales = profitRow.getAs[Double]("Avg_Sales")
      val avgProfit = profitRow.getAs[Double]("Avg_Profit")
      val avgCOGS = profitRow.getAs[Double]("Avg_COGS")
      val profitMargin = profitRow.getAs[Double]("Profit_Margin_Percent")
      
      results.append("4. 整体利润和售价分析:\n")
      results.append(f"   平均销售额: $avgSales%.2f\n")
      results.append(f"   平均利润: $avgProfit%.2f\n")
      results.append(f"   平均成本: $avgCOGS%.2f\n")
      results.append(f"   利润率: $profitMargin%.2f%%\n\n")
      
      // 5. 产品类型分析
      println("\n5. 分析产品类型...")
      val productTypeAnalysis = cleanedDF.groupBy("Product Type")
        .agg(
          sum("Coffee Sales").alias("Total_Sales"),
          avg("Coffee Sales").alias("Avg_Sales"),
          avg("Profit").alias("Avg_Profit"),
          count("*").alias("Transaction_Count")
        )
        .orderBy(desc("Total_Sales"))
      
      println("产品类型分析:")
      productTypeAnalysis.show()
      
      results.append("5. 产品类型分析:\n")
      productTypeAnalysis.collect().foreach { row =>
        val productType = row.getAs[String]("Product Type")
        val totalSales = row.get(1).asInstanceOf[Number].doubleValue()
        val avgSales = row.get(2).asInstanceOf[Number].doubleValue()
        val avgProfit = row.get(3).asInstanceOf[Number].doubleValue()
        val count = row.getAs[Long]("Transaction_Count")
        results.append(f"   $productType: 总销售额=$totalSales%.2f, 平均销售额=$avgSales%.2f, 平均利润=$avgProfit%.2f, 交易次数=$count\n")
      }
      results.append("\n")
      
      // 保存所有分析结果到文件
      val writer = new PrintWriter(new File(s"${outputDir}coffee_analysis_report.txt"))
      try {
        writer.write(results.toString())
        println(s"\n完整分析报告已保存到: ${outputDir}coffee_analysis_report.txt")
      } finally {
        writer.close()
      }
      
      // 保存各种分析结果到CSV文件
      stateAnalysis.coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"${outputDir}state_analysis")
      
      marketAnalysis.coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"${outputDir}market_analysis")
      
      productTypeAnalysis.coalesce(1)
        .write
        .mode("overwrite")
        .option("header", "true")
        .csv(s"${outputDir}product_type_analysis")
      
      println("\n=== 分析完成 ===")
      println("生成的文件:")
      println(s"- 完整报告: ${outputDir}coffee_analysis_report.txt")
      println(s"- 销售排名: ${outputDir}coffee_sales_ranking/")
      println(s"- 州分析: ${outputDir}state_analysis/")
      println(s"- 市场分析: ${outputDir}market_analysis/")
      println(s"- 产品类型分析: ${outputDir}product_type_analysis/")
      
    } catch {
      case e: Exception =>
        println(s"分析过程中发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
    }
  }
}
