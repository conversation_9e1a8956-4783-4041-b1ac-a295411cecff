#!/bin/bash

echo "=== 修复咖啡数据分析程序 ==="
echo "问题1：CSV文件中的列名是'Coffee Sales'而不是'Sales'"
echo "问题2：类型转换错误 - Long无法转换为Double"
echo "解决方案：修复列名和类型转换问题，重新编译程序"
echo ""

# 确保在正确目录
cd /home/<USER>/spark_work

echo "当前目录: $(pwd)"
echo "检查修复后的文件..."
ls -la CoffeeChainAnalysis.scala

echo ""
echo "删除旧的class文件..."
rm -f CoffeeChainAnalysis*.class

echo "重新编译 CoffeeChainAnalysis.scala..."
scalac -cp "$SPARK_HOME/jars/*" CoffeeChainAnalysis.scala

if [ $? -eq 0 ]; then
    echo "✓ 编译成功"
    
    echo ""
    echo "检查生成的class文件:"
    ls -la CoffeeChainAnalysis*.class
    
    echo ""
    echo "运行修复后的咖啡数据分析程序..."
    spark-submit --class CoffeeChainAnalysis --master local[*] --driver-memory 1g ./
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✓ 咖啡数据分析程序运行成功！"
        echo ""
        echo "生成的结果文件："
        ls -la coffee_analysis_report.txt 2>/dev/null && echo "- 分析报告: coffee_analysis_report.txt"
        ls -la *_analysis/ 2>/dev/null && echo "- CSV分析结果目录已生成"
        
        echo ""
        echo "查看分析报告前20行："
        head -20 coffee_analysis_report.txt 2>/dev/null || echo "未找到分析报告文件"
        
    else
        echo "✗ 程序运行失败"
    fi
    
else
    echo "✗ 编译失败"
    echo "请检查错误信息"
fi

echo ""
echo "修复完成！"
