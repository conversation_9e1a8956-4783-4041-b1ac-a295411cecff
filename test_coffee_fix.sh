#!/bin/bash

echo "=== 测试咖啡数据分析修复 ==="
echo "修复内容："
echo "1. 列名：Sales -> Coffee Sales"
echo "2. 类型转换：使用 .asInstanceOf[Number].doubleValue() 处理聚合结果"
echo ""

cd /home/<USER>/spark_work

echo "当前目录: $(pwd)"
echo ""

echo "删除旧的编译文件..."
rm -f CoffeeChainAnalysis*.class

echo "重新编译程序..."
scalac -cp "$SPARK_HOME/jars/*" CoffeeChainAnalysis.scala

if [ $? -eq 0 ]; then
    echo "✓ 编译成功"
    
    echo ""
    echo "检查CSV文件是否存在..."
    if [ -f "CoffeeChain.csv" ]; then
        echo "✓ CoffeeChain.csv 文件存在"
        echo "文件大小: $(wc -l < CoffeeChain.csv) 行"
        echo "文件头部："
        head -3 CoffeeChain.csv
    else
        echo "✗ CoffeeChain.csv 文件不存在"
        echo "请确保CSV文件在当前目录"
        exit 1
    fi
    
    echo ""
    echo "运行咖啡数据分析程序..."
    echo "开始时间: $(date)"
    
    spark-submit --class CoffeeChainAnalysis --master local[2] --driver-memory 1g ./
    
    RESULT=$?
    echo "结束时间: $(date)"
    
    if [ $RESULT -eq 0 ]; then
        echo ""
        echo "✓ 程序运行成功！"
        
        echo ""
        echo "检查生成的文件："
        
        if [ -f "coffee_analysis_report.txt" ]; then
            echo "✓ 分析报告已生成"
            echo "报告大小: $(wc -l < coffee_analysis_report.txt) 行"
            echo ""
            echo "报告前15行内容："
            head -15 coffee_analysis_report.txt
        else
            echo "✗ 分析报告未生成"
        fi
        
        echo ""
        echo "检查CSV分析结果目录："
        for dir in coffee_sales_ranking state_analysis market_analysis product_type_analysis; do
            if [ -d "$dir" ]; then
                echo "✓ $dir/ 目录已生成"
                ls -la "$dir"/*.csv 2>/dev/null | head -2
            else
                echo "✗ $dir/ 目录未生成"
            fi
        done
        
    else
        echo "✗ 程序运行失败，退出码: $RESULT"
    fi
    
else
    echo "✗ 编译失败"
fi

echo ""
echo "测试完成！"
