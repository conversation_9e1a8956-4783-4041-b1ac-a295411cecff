import org.apache.spark.sql.SparkSession
import scala.util.Random
import java.io.{File, PrintWriter}

object GeneratePeopleData {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("Generate People Data")
      .master("local[*]")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .getOrCreate()

    try {
      // 设置日志级别
      spark.sparkContext.setLogLevel("WARN")
      
      println("开始生成人口年龄数据...")
      
      // 生成数据的行数（可以根据需要调整）
      val numRecords = if (args.length > 0) args(0).toInt else 1000
      
      // 输出文件路径
      val outputPath = "/home/<USER>/spark_work/peopleage.txt"
      
      // 创建输出目录（如果不存在）
      val outputDir = new File("/home/<USER>/spark_work/")
      if (!outputDir.exists()) {
        outputDir.mkdirs()
        println(s"创建目录: ${outputDir.getAbsolutePath}")
      }
      
      // 生成随机年龄数据
      val random = new Random()
      val writer = new PrintWriter(new File(outputPath))
      
      try {
        for (i <- 1 to numRecords) {
          // 生成18-90岁之间的随机年龄
          val age = 18 + random.nextInt(73) // 18 + (0-72) = 18-90
          writer.println(s"$i\t$age")
          
          // 每1000行打印一次进度
          if (i % 1000 == 0) {
            println(s"已生成 $i 行数据...")
          }
        }
        
        println(s"成功生成 $numRecords 行人口年龄数据")
        println(s"数据文件保存在: $outputPath")
        
        // 显示前10行数据作为示例
        println("\n前10行数据示例:")
        val source = scala.io.Source.fromFile(outputPath)
        try {
          source.getLines().take(10).foreach(println)
        } finally {
          source.close()
        }
        
      } finally {
        writer.close()
      }
      
      // 使用Spark验证生成的数据
      println("\n使用Spark验证数据...")
      val df = spark.read
        .option("delimiter", "\t")
        .option("header", "false")
        .csv(outputPath)
        .toDF("id", "age")
      
      val count = df.count()
      println(s"Spark读取到的数据行数: $count")
      
      // 显示数据统计信息
      df.select("age").describe().show()
      
      println("数据生成完成！")
      
    } catch {
      case e: Exception =>
        println(s"生成数据时发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
    }
  }
}
