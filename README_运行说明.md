# Spark数据分析项目运行说明

## 项目概述
本项目包含3个Scala程序，用于完成Spark数据处理和分析任务：

1. **GeneratePeopleData.scala** - 生成人口年龄数据文件
2. **CalculateAverageAge.scala** - 计算人口平均年龄（使用RDD编程）
3. **CoffeeChainAnalysis.scala** - 咖啡连锁店数据分析

## 环境要求

### CentOS虚拟机环境
- CentOS 7/8
- Java 8 或更高版本
- Apache Spark 2.4+ 或 3.x
- Scala 2.12+

### 安装步骤

#### 1. 安装Java
```bash
# 安装OpenJDK 8
sudo yum install java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 验证安装
java -version
```

#### 2. 安装Scala
```bash
# 下载并安装Scala
wget https://downloads.lightbend.com/scala/2.12.17/scala-2.12.17.tgz
tar -xzf scala-2.12.17.tgz
sudo mv scala-2.12.17 /opt/scala

# 设置环境变量
echo 'export SCALA_HOME=/opt/scala' >> ~/.bashrc
echo 'export PATH=$SCALA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

#### 3. 安装Spark
```bash
# 下载Spark
wget https://archive.apache.org/dist/spark/spark-3.2.4/spark-3.2.4-bin-hadoop3.2.tgz
tar -xzf spark-3.2.4-bin-hadoop3.2.tgz
sudo mv spark-3.2.4-bin-hadoop3.2 /opt/spark

# 设置环境变量
echo 'export SPARK_HOME=/opt/spark' >> ~/.bashrc
echo 'export PATH=$SPARK_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 验证安装
spark-submit --version
```

## 文件说明

### 源代码文件
- `GeneratePeopleData.scala` - 数据生成程序
- `CalculateAverageAge.scala` - 平均年龄计算程序
- `CoffeeChainAnalysis.scala` - 咖啡数据分析程序
- `CoffeeChain.csv` - 咖啡连锁店原始数据
- `run_spark_analysis.sh` - 自动运行脚本

### 输出文件（将在/home/<USER>/spark_work/目录生成）
- `peopleage.txt` - 生成的人口年龄数据
- `average_age_result.txt` - 平均年龄计算结果
- `coffee_analysis_report.txt` - 咖啡数据分析报告
- `coffee_sales_ranking/` - 咖啡销售排名CSV
- `state_analysis/` - 各州销售分析CSV
- `market_analysis/` - 市场分析CSV
- `product_type_analysis/` - 产品类型分析CSV

## 运行方法

### 方法1: 使用自动运行脚本（推荐）
```bash
# 1. 将所有文件复制到虚拟机
scp *.scala *.csv *.sh user@your-vm-ip:/home/<USER>/

# 2. 登录虚拟机
ssh user@your-vm-ip

# 3. 给脚本执行权限
chmod +x run_spark_analysis.sh

# 4. 运行脚本
./run_spark_analysis.sh
```

### 方法2: 手动运行每个程序
```bash
# 创建工作目录
mkdir -p /home/<USER>/spark_work
cd /home/<USER>/spark_work

# 复制文件
cp /path/to/your/files/*.scala .
cp /path/to/your/files/CoffeeChain.csv .

# 1. 生成人口数据（生成1000行数据）
spark-submit --class GeneratePeopleData --master local[*] GeneratePeopleData.scala 1000

# 2. 计算平均年龄
spark-submit --class CalculateAverageAge --master local[*] CalculateAverageAge.scala

# 3. 咖啡数据分析
spark-submit --class CoffeeChainAnalysis --master local[*] CoffeeChainAnalysis.scala
```

### 方法3: 使用Scala编译器
```bash
# 编译Scala文件
scalac -cp "$SPARK_HOME/jars/*" GeneratePeopleData.scala
scalac -cp "$SPARK_HOME/jars/*" CalculateAverageAge.scala
scalac -cp "$SPARK_HOME/jars/*" CoffeeChainAnalysis.scala

# 运行编译后的类文件
spark-submit --class GeneratePeopleData --master local[*] .
spark-submit --class CalculateAverageAge --master local[*] .
spark-submit --class CoffeeChainAnalysis --master local[*] .
```

## 程序功能详解

### 1. GeneratePeopleData.scala
- **功能**: 生成指定行数的人口年龄数据
- **输入**: 可选参数指定生成行数（默认1000行）
- **输出**: `/home/<USER>/spark_work/peopleage.txt`
- **格式**: 每行包含序号和年龄，用制表符分隔
- **年龄范围**: 18-90岁随机生成

### 2. CalculateAverageAge.scala
- **功能**: 使用RDD和DataFrame两种方式计算平均年龄
- **输入**: `/home/<USER>/spark_work/peopleage.txt`
- **输出**: `/home/<USER>/spark_work/average_age_result.txt`
- **分析内容**:
  - 平均年龄
  - 最大/最小年龄
  - 年龄分布统计
  - 年龄段分布

### 3. CoffeeChainAnalysis.scala
- **功能**: 综合分析咖啡连锁店数据
- **输入**: `CoffeeChain.csv`
- **输出**: 多个分析结果文件
- **分析内容**:
  - 数据预处理和清洗
  - 咖啡销售量排名
  - 销售量与州的关系
  - 销售量与市场的关系
  - 平均利润和售价分析
  - 产品类型分析

## 故障排除

### 常见问题
1. **Java版本问题**: 确保使用Java 8或更高版本
2. **内存不足**: 可以调整Spark配置 `--driver-memory 2g --executor-memory 2g`
3. **文件路径问题**: 确保所有文件路径正确，特别是CoffeeChain.csv
4. **权限问题**: 确保对/home/<USER>/spark_work/目录有写权限

### 调试命令
```bash
# 检查Java版本
java -version

# 检查Spark安装
spark-submit --version

# 检查文件是否存在
ls -la /home/<USER>/spark_work/

# 查看Spark日志
tail -f /opt/spark/logs/spark-*.log
```

## 预期结果

运行成功后，您将在`/home/<USER>/spark_work/`目录下看到：
- 人口数据文件和分析结果
- 咖啡数据的详细分析报告
- 各种CSV格式的分析结果

每个程序都会在控制台输出详细的执行过程和结果摘要。
