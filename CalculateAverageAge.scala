import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._
import org.apache.spark.sql.types._
import java.io.{File, PrintWriter}

object CalculateAverageAge {
  def main(args: Array[String]): Unit = {
    // 创建SparkSession
    val spark = SparkSession.builder()
      .appName("Calculate Average Age")
      .master("local[*]")
      .config("spark.sql.adaptive.enabled", "true")
      .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
      .getOrCreate()

    import spark.implicits._

    try {
      // 设置日志级别
      spark.sparkContext.setLogLevel("WARN")
      
      println("开始计算人口平均年龄...")
      
      // 输入文件路径
      val inputPath = "/home/<USER>/spark_work/peopleage.txt"
      val outputPath = "/home/<USER>/spark_work/average_age_result.txt"
      
      // 检查输入文件是否存在
      val inputFile = new File(inputPath)
      if (!inputFile.exists()) {
        println(s"错误: 输入文件不存在 - $inputPath")
        println("请先运行 GeneratePeopleData.scala 生成数据文件")
        return
      }
      
      println(s"读取数据文件: $inputPath")
      
      // 定义数据模式
      val schema = StructType(Array(
        StructField("id", IntegerType, true),
        StructField("age", IntegerType, true)
      ))
      
      // 读取数据文件
      val df = spark.read
        .option("delimiter", "\t")
        .option("header", "false")
        .schema(schema)
        .csv(inputPath)
      
      // 缓存数据以提高性能
      df.cache()
      
      val totalRecords = df.count()
      println(s"总记录数: $totalRecords")
      
      // 显示前10行数据
      println("\n前10行数据:")
      df.show(10)
      
      // 计算平均年龄
      println("正在计算平均年龄...")
      val avgAge = df.select(avg("age")).collect()(0)(0).asInstanceOf[Double]
      
      // 计算其他统计信息
      val stats = df.select(
        min("age").alias("min_age"),
        max("age").alias("max_age"),
        avg("age").alias("avg_age"),
        count("age").alias("total_count")
      ).collect()(0)
      
      val minAge = stats.getAs[Int]("min_age")
      val maxAge = stats.getAs[Int]("max_age")
      val totalCount = stats.getAs[Long]("total_count")
      
      // 计算年龄分布
      println("\n年龄分布统计:")
      val ageDistribution = df.groupBy("age")
        .count()
        .orderBy("age")
      
      ageDistribution.show(20)
      
      // 按年龄段统计
      val ageGroups = df.withColumn("age_group", 
        when(col("age") < 30, "18-29")
        .when(col("age") < 40, "30-39")
        .when(col("age") < 50, "40-49")
        .when(col("age") < 60, "50-59")
        .when(col("age") < 70, "60-69")
        .otherwise("70+")
      ).groupBy("age_group")
       .count()
       .orderBy("age_group")
      
      println("\n年龄段分布:")
      ageGroups.show()
      
      // 准备结果输出
      val results = StringBuilder.newBuilder
      results.append("=== 人口年龄统计分析结果 ===\n")
      results.append(s"分析时间: ${java.time.LocalDateTime.now()}\n")
      results.append(s"数据文件: $inputPath\n")
      results.append(s"总人口数: $totalCount\n")
      results.append(f"平均年龄: $avgAge%.2f 岁\n")
      results.append(s"最小年龄: $minAge 岁\n")
      results.append(s"最大年龄: $maxAge 岁\n")
      results.append(s"年龄范围: ${maxAge - minAge} 岁\n\n")
      
      results.append("年龄段分布:\n")
      ageGroups.collect().foreach { row =>
        val ageGroup = row.getAs[String]("age_group")
        val count = row.getAs[Long]("count")
        val percentage = (count.toDouble / totalCount * 100)
        results.append(f"$ageGroup: $count 人 (${percentage}%.1f%%)\n")
      }
      
      // 保存结果到文件
      val writer = new PrintWriter(new File(outputPath))
      try {
        writer.write(results.toString())
        println(s"\n结果已保存到: $outputPath")
      } finally {
        writer.close()
      }
      
      // 在控制台显示主要结果
      println("\n=== 计算结果 ===")
      println(f"人口平均年龄: $avgAge%.2f 岁")
      println(s"统计人口总数: $totalCount 人")
      println(s"年龄范围: $minAge - $maxAge 岁")
      
      // 使用RDD方式验证计算结果
      println("\n使用RDD方式验证结果...")
      val rdd = spark.sparkContext.textFile(inputPath)
      val ageRDD = rdd.map(line => {
        val parts = line.split("\t")
        parts(1).toInt
      })
      
      val rddAvgAge = ageRDD.sum() / ageRDD.count()
      println(f"RDD计算的平均年龄: $rddAvgAge%.2f 岁")
      
      println("\n计算完成！")
      
    } catch {
      case e: Exception =>
        println(s"计算平均年龄时发生错误: ${e.getMessage}")
        e.printStackTrace()
    } finally {
      spark.stop()
    }
  }
}
