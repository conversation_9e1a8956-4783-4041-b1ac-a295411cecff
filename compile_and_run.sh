#!/bin/bash

# 简化的编译和运行脚本
echo "=== Spark Scala程序编译和运行脚本 ==="

# 设置工作目录
WORK_DIR="/home/<USER>/spark_work"
cd $WORK_DIR

echo "当前工作目录: $(pwd)"
echo "检查文件..."
ls -la *.scala

# 检查scalac是否可用
if ! command -v scalac &> /dev/null; then
    echo "错误: 未找到scalac命令，请安装Scala"
    exit 1
fi

# 检查spark-submit是否可用
if ! command -v spark-submit &> /dev/null; then
    echo "错误: 未找到spark-submit命令，请检查Spark安装"
    exit 1
fi

echo ""
echo "=== 步骤1: 编译Scala文件 ==="

# 编译第一个文件
echo "编译 GeneratePeopleData.scala..."
scalac -cp "$SPARK_HOME/jars/*" GeneratePeopleData.scala
if [ $? -eq 0 ]; then
    echo "✓ GeneratePeopleData.scala 编译成功"
else
    echo "✗ GeneratePeopleData.scala 编译失败"
    exit 1
fi

# 编译第二个文件
echo "编译 CalculateAverageAge.scala..."
scalac -cp "$SPARK_HOME/jars/*" CalculateAverageAge.scala
if [ $? -eq 0 ]; then
    echo "✓ CalculateAverageAge.scala 编译成功"
else
    echo "✗ CalculateAverageAge.scala 编译失败"
    exit 1
fi

# 编译第三个文件
echo "重新编译 CoffeeChainAnalysis.scala..."
rm -f CoffeeChainAnalysis*.class
scalac -cp "$SPARK_HOME/jars/*" CoffeeChainAnalysis.scala
if [ $? -eq 0 ]; then
    echo "✓ CoffeeChainAnalysis.scala 重新编译成功"
else
    echo "✗ CoffeeChainAnalysis.scala 编译失败"
    exit 1
fi

echo ""
echo "编译完成！生成的class文件："
ls -la *.class

echo ""
echo "=== 步骤2: 运行程序 ==="

# 运行第一个程序
echo ""
echo "--- 运行任务1: 生成人口数据 ---"
spark-submit --class GeneratePeopleData --master local[*] --driver-memory 1g ./ 1000

# 检查第一个程序的输出
if [ -f "peopleage.txt" ]; then
    echo "✓ peopleage.txt 生成成功"
    echo "文件大小: $(wc -l < peopleage.txt) 行"
    echo "前5行内容:"
    head -5 peopleage.txt
else
    echo "✗ peopleage.txt 生成失败"
fi

echo ""
echo "--- 运行任务2: 计算平均年龄 ---"
spark-submit --class CalculateAverageAge --master local[*] --driver-memory 1g ./

# 检查第二个程序的输出
if [ -f "average_age_result.txt" ]; then
    echo "✓ average_age_result.txt 生成成功"
    echo "结果内容:"
    cat average_age_result.txt
else
    echo "✗ average_age_result.txt 生成失败"
fi

echo ""
echo "--- 运行任务3: 咖啡数据分析 ---"
if [ -f "CoffeeChain.csv" ]; then
    spark-submit --class CoffeeChainAnalysis --master local[*] --driver-memory 1g ./
    
    # 检查第三个程序的输出
    if [ -f "coffee_analysis_report.txt" ]; then
        echo "✓ coffee_analysis_report.txt 生成成功"
        echo "报告文件大小: $(wc -l < coffee_analysis_report.txt) 行"
    else
        echo "✗ coffee_analysis_report.txt 生成失败"
    fi
else
    echo "✗ 未找到CoffeeChain.csv文件，跳过咖啡数据分析"
    echo "请将CoffeeChain.csv文件复制到当前目录"
fi

echo ""
echo "=== 执行完成 ==="
echo "生成的所有文件："
ls -la *.txt *.csv 2>/dev/null || echo "未找到输出文件"

echo ""
echo "生成的分析目录："
ls -la *_analysis/ 2>/dev/null || echo "未找到分析目录"
